# 物料入库单申请到确认流程详细分析

## 概述

本文档详细分析物料管理中从物料入库单申请到物料入库单确认的完整流程，包括创建权限、审核权限、状态流转和业务逻辑。

## 流程概览

```mermaid
graph TD
    A[创建入库单] --> B[保存状态]
    B --> C[提交入库单]
    C --> D[待确认状态]
    D --> E{仓库管理员审核}
    E -->|确认| F[已确认状态]
    E -->|退回| G[退回状态]
    G --> B
    F --> H[库存更新]
```

## 核心数据表

### 1. UTWLOrder - 物料单据主表
**表用途**: 存储所有物料单据的主要信息

**关键字段**:
- `ID` (Guid, PK): 单据ID
- `OrderType` (Int32): 单据类型（1=入库单）
- `OrderCode` (String): 单据编号
- `OrderClass` (String): 单据分类（采购入库、期初入库、其他入库、归还入库）
- `OrderTitle` (String): 单据标题
- `OrderDesc` (String): 单据描述
- `UnitID` (Guid, FK): 项目单位ID
- `WHID` (Guid, FK): 仓库ID
- `Status` (Int32): 单据状态
- `CreateUser` (Int64, FK): 创建人ID
- `CreateDate` (DateTime): 创建时间
- `SubmitUser` (Int64, FK): 提交人ID
- `SubmitDate` (DateTime): 提交时间
- `ConfirmUser` (Int64, FK): 确认人ID
- `ConfirmDate` (DateTime): 确认时间
- `ConfirmDesc` (String): 确认说明
- `ApplyUser` (Int64, FK): 申请人ID
- `IsDelete` (Int32): 删除标记

### 2. UTWLOrderDetial - 物料单据明细表
**表用途**: 存储单据的物料明细信息

**关键字段**:
- `ID` (Guid, PK): 明细ID
- `OrderID` (Guid, FK): 关联单据ID
- `ItemID` (Guid, FK): 物料ID
- `Qty` (Decimal): 数量
- `Price` (Decimal): 单价
- `Amount` (Decimal): 金额
- `Remark` (String): 备注

### 3. UTWLWareHouse - 仓库表
**表用途**: 仓库基础信息和管理员配置

**关键字段**:
- `ID` (Guid, PK): 仓库ID
- `UnitID` (Guid, FK): 项目单位ID
- `WHCode` (String): 仓库编码
- `WHName` (String): 仓库名称
- `WHDesc` (String): 仓库描述
- `ManageUser` (Int64, FK): **仓库管理员ID**
- `IsDelete` (Int32): 删除标记

## 单据状态枚举

```csharp
public enum EnumWLOrderStatus
{
    保存 = 0,      // 草稿状态，可编辑
    已提交 = 1,    // 已提交，等待确认
    退回 = 2,      // 被退回，需要重新处理
    已确认 = 3,    // 已确认，流程完成
    待确认 = 5     // 待确认状态
}
```

## 详细流程分析

### 第一阶段：创建入库单

#### 1.1 创建权限
**谁能创建**：
- 所有有物料管理模块权限的用户
- 基于项目权限控制，只能在自己有权限的项目中创建
- 通过 `SessionManager.CurrentUserProjects` 获取用户项目权限

**创建逻辑**：
<augment_code_snippet path="PropertySys_ZGHQ.Web/Areas/ModuleWL/Models/ModelOrder.cs" mode="EXCERPT">
````csharp
public void Save()
{
    if (UTWLOrderEnt.ID == null || UTWLOrderEnt.ID == Guid.Empty)
    {
        UTWLOrderEnt.UnitID = SessionManager.CurrentUserAccount.UnitID;
        UTWLOrderEnt.CreateDate = DateTime.Now;
        UTWLOrderEnt.CreateUser = SessionManager.CurrentUser.UserID;
    }
    UTWLOrderEnt = serviceUTWLOrder.SaveByUser(UTWLOrderEnt);
}
````
</augment_code_snippet>

#### 1.2 入库单类型
系统支持4种入库单类型：
- **采购入库**: 采购物料的入库
- **期初入库**: 系统初始化时的库存录入
- **其他入库**: 其他方式的入库
- **归还入库**: 归还物料的入库

### 第二阶段：提交入库单

#### 2.1 提交权限
**谁能提交**：
- 入库单的创建人
- 有物料管理权限的相关人员

#### 2.2 提交逻辑
<augment_code_snippet path="PropertySys_ZGHQ.Web/Areas/ModuleWL/Controllers/OrderController.cs" mode="EXCERPT">
````csharp
[HttpPost]
public ActionResult SubmitOrder(Guid id)
{
    UTWLOrder ent = serviceUTWLOrder.GetByID(id);
    string msg = serviceUTWLOrder.SubmitWLOrder(id, SessionManager.CurrentUserAccount);
    jsresult.Data = new { result = msg };
    return jsresult;
}
````
</augment_code_snippet>

#### 2.3 提交验证
- 验证单据状态必须为"保存"状态
- 验证单据必须有明细数据
- 验证物料信息完整性
- 更新单据状态为"已提交"或"待确认"

### 第三阶段：仓库管理员确认

#### 3.1 确认权限
**谁能确认**：
- **仓库管理员**: 在 `UTWLWareHouse` 表中 `ManageUser` 字段指定的用户
- 权限验证逻辑：

<augment_code_snippet path="PropertySys_ZGHQ.Web/Areas/ModuleWL/Models/ModelOrder.cs" mode="EXCERPT">
````csharp
// 仓库管理员权限验证
Count = serviceUVWLOrderOutput.GetWLOrderOutputCount(SearchEntity, SessionManager.CurrentUserAccount.UserID);
GridDataSources = serviceUVWLOrderOutput.GetWLOrderOutput(SearchEntity, SessionManager.CurrentUserAccount.UserID);
````
</augment_code_snippet>

#### 3.2 确认操作
**确认入口**：
- **PC端**: `~/ModuleWL/WareManager/Index` - 仓库管理员确认页面
- **移动端**: 微信小程序端确认功能

**确认逻辑**：
<augment_code_snippet path="PropertySys_ZGHQ.Web/Areas/ModuleWL/Controllers/WareManagerController.cs" mode="EXCERPT">
````csharp
[HttpPost]
public ActionResult CheckOrderOutput(Guid id, int CheckType, string configdesc)
{
    UTWLOrder ent = serviceUTWLOrder.GetByID(id);
    if (CheckType == 2) // 退回
    {
        ent.Status = 0; // 重置为保存状态
        ent.ConfirmUser = SessionManager.CurrentUserAccount.UserID;
        ent.ConfirmDate = DateTime.Now;
        ent.ConfirmDesc = configdesc;
        serviceUTWLOrder.Update(ent);
    }
    else // 确认
    {
        string msg = serviceUTWLOrder.SubmitWLOrderOutput(id, SessionManager.CurrentUserAccount, configdesc);
    }
}
````
</augment_code_snippet>

#### 3.3 确认结果
**确认通过**：
- 单据状态更新为"已确认"
- 更新库存数据
- 记录确认人和确认时间
- 发送确认通知

**退回处理**：
- 单据状态重置为"保存"
- 记录退回原因
- 记录操作日志
- 发送退回通知给申请人

## 权限控制详细分析

### 1. 项目级权限控制
```csharp
// 用户只能操作自己有权限的项目
UTBaseUnitManageList = SessionManager.CurrentUserProjects;
condition.And(x => x.UnitID == SessionManager.CurrentUserAccount.UnitID);
```

### 2. 仓库管理员权限
```csharp
// 仓库管理员通过UTWLWareHouse.ManageUser字段控制
UTWLWareHouse wareHouse = serviceUTWLWareHouse.GetByID(warehouseId);
bool isWareManager = wareHouse.ManageUser == SessionManager.CurrentUserAccount.UserID;
```

### 3. 数据过滤机制
- **创建阶段**: 基于用户项目权限过滤可选仓库
- **查看阶段**: 只能查看自己项目的入库单
- **确认阶段**: 只有对应仓库的管理员才能确认

## 业务规则

### 1. 状态流转规则
- `保存` → `已提交/待确认` → `已确认`
- `保存` → `已提交/待确认` → `退回` → `保存`
- 只有"保存"状态的单据可以编辑
- 只有"已提交/待确认"状态的单据可以确认

### 2. 库存更新规则
- 入库单确认后自动增加库存
- 支持按仓库、物料分别管理库存
- 记录库存变动历史

### 3. 通知机制
- 提交后通知仓库管理员
- 确认后通知申请人
- 退回后通知创建人
- 支持微信消息推送

## 移动端支持

### 微信小程序功能
- **入库单创建**: 支持移动端创建入库单
- **入库单提交**: 支持移动端提交功能
- **仓库管理员确认**: 支持扫码确认入库
- **状态查询**: 实时查询入库单状态

### 移动端权限
- 基于微信OpenID关联用户账号
- 继承PC端的所有权限控制
- 支持离线操作和数据同步

## 操作日志

### 日志记录
系统通过 `UTUnitOperateRecord` 表记录所有关键操作：
- 入库单创建
- 入库单提交
- 入库单确认
- 入库单退回

### 日志内容
- 操作时间
- 操作人员
- 操作类型
- 操作描述
- 关联单据ID

## 总结

物料入库单流程涉及**多个角色**和**严格的权限控制**：

### 创建权限
- **普通用户**: 有物料管理模块权限的用户
- **项目限制**: 只能在有权限的项目中创建
- **仓库限制**: 只能选择项目内的仓库

### 审核权限  
- **仓库管理员**: UTWLWareHouse表中ManageUser字段指定的用户
- **单一责任**: 每个仓库只有一个管理员
- **项目隔离**: 仓库管理员只能确认自己管理仓库的入库单

### 流程特点
1. **状态驱动**: 严格按照状态流转
2. **权限分离**: 创建和确认权限分离
3. **数据隔离**: 基于项目的数据权限控制
4. **审计完整**: 完整的操作日志记录
5. **多端支持**: PC端和移动端双重支持

这种设计确保了物料入库流程的**安全性**、**可控性**和**可追溯性**。
